package localcon_T_fla
{
   import adobe.utils.*;
   import fl.controls.Button;
   import fl.controls.ComboBox;
   import fl.controls.Label;
   import fl.controls.TextArea;
   import fl.controls.TextInput;
   import fl.data.DataProvider;
   import fl.data.SimpleCollectionItem;
   import flash.accessibility.*;
   import flash.desktop.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.globalization.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.printing.*;
   import flash.profiler.*;
   import flash.sampler.*;
   import flash.sensors.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.engine.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   import flash.xml.*;
   
   public dynamic class MainTimeline extends MovieClip
   {
      public var P:ComboBox;
      
      public var __id0_:Label;
      
      public var __id1_:Label;
      
      public var data_check:ComboBox;
      
      public var data_xml:ComboBox;
      
      public var id:TextInput;
      
      public var num:TextInput;
      
      public var output:TextArea;
      
      public var send_bt:Button;
      
      public var local_con:LocalConnection;
      
      public var obj:Object;
      
      public var xml_data:DataProvider;
      
      public var xml:XML;
      
      public function MainTimeline()
      {
         super();
         addFrameScript(0,this.frame1);
         this.__setProp_send_bt_场景1_图层1_0();
         this.__setProp_data_check_场景1_图层1_0();
         this.__setProp___id0__场景1_图层1_0();
         this.__setProp___id1__场景1_图层1_0();
         this.__setProp_data_xml_场景1_图层1_0();
         this.__setProp_P_场景1_图层1_0();
      }
      
      public function onStatus(param1:StatusEvent) : void
      {
         switch(param1.level)
         {
            case "status":
               this.output.text = "LocalConnection.send() succeeded";
               break;
            case "error":
               this.output.text = "LocalConnection.send() failed";
         }
      }
      
      public function send(param1:MouseEvent) : void
      {
         // 清空输出区域
         this.output.text = "";

         // 构建发送对象
         this.obj["P"] = this.P.selectedIndex + 1;
         this.obj["type"] = this.data_check.selectedIndex + 1;
         if(this.id.text == "")
         {
            this.obj["id"] = this.data_xml.selectedItem["id"];
         }
         else
         {
            this.obj["id"] = this.id.text;
         }
         this.obj["num"] = this.num.text;

         // 显示发送的数据
         var debugInfo:String = "=== 发送数据 ===\n";
         debugInfo += "玩家: P" + this.obj["P"] + "\n";
         debugInfo += "功能: " + getFunctionName(this.obj["type"]) + "\n";
         debugInfo += "ID: " + this.obj["id"] + "\n";
         debugInfo += "数量: " + this.obj["num"] + "\n";
         debugInfo += "发送时间: " + new Date().toTimeString() + "\n";
         debugInfo += "==================\n\n";

         this.output.text = debugInfo;

         // 发送数据
         try {
            this.local_con.send("my_cons","save",this.obj);
            this.output.appendText("数据发送成功！\n");
            this.output.appendText("请查看Flash控制台获取详细调试信息...\n");
         } catch (e:Error) {
            this.output.appendText("发送失败: " + e.message + "\n");
         }
      }

      // 获取功能名称
      private function getFunctionName(type:int):String
      {
         var functionNames:Array = [
            "", // 0 - 无效
            "添加武器装备", // 1
            "清理武器装备背包", // 2
            "添加衣服装备", // 3
            "清理衣服装备背包", // 4
            "添加项链装备", // 5
            "清理项链装备背包", // 6
            "添加葫芦装备", // 7
            "清理葫芦装备背包", // 8
            "添加宝石", // 9
            "清理宝石背包", // 10
            "添加消耗品", // 11
            "清理消耗品背包", // 12
            "添加其他道具", // 13
            "清理其他道具背包", // 14
            "添加任务道具", // 15
            "清理任务道具背包", // 16
            "添加宠物", // 17
            "添加宠物装备", // 18
            "修改金币", // 19
            "修改等级", // 20
            "修改经验", // 21
            "修改血量", // 22
            "修改魔法值", // 23
            "无敌模式", // 24
            "秒杀模式", // 25
            "解锁所有技能", // 26
            "技能冷却清零", // 27
            "修改攻击力", // 28
            "修改防御力", // 29
            "修复背包", // 30
            "列出装备ID", // 31
            "输出所有装备ID", // 32
            "输出常用装备ID" // 33
         ];

         if (type >= 0 && type < functionNames.length) {
            return functionNames[type];
         }
         return "未知功能(" + type + ")";
      }
      
      public function Load_Config() : void
      {
         var _loc1_:URLLoader = new URLLoader();
         _loc1_.addEventListener(Event.COMPLETE,this.set_xml);
         _loc1_.load(new URLRequest("good.xml"));
         _loc1_.dataFormat = URLLoaderDataFormat.TEXT;
      }
      
      public function set_xml(param1:Event) : void
      {
         this.xml = XML(param1.target.data);
         this.xml_data = new DataProvider(this.xml.children()[0]);
         this.data_xml.dataProvider = this.xml_data;
         this.data_xml.labelField = "name";
         this.data_check.addEventListener(Event.CHANGE,this.one_change);
      }
      
      public function one_change(param1:Event) : void
      {
         trace(param1.target.selectedIndex);
         switch(param1.target.selectedIndex)
         {
            case 0: // 武器装备
               this.xml_data = new DataProvider(this.xml.children()[0]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 1: // 清理武器装备
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 2: // 衣服装备
               this.xml_data = new DataProvider(this.xml.children()[1]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 3: // 清理衣服装备
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 4: // 项链装备
               this.xml_data = new DataProvider(this.xml.children()[2]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 5: // 清理项链装备
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 6: // 葫芦装备
               this.xml_data = new DataProvider(this.xml.children()[3]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 7: // 清理葫芦装备
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 8: // 宝石
               this.xml_data = new DataProvider(this.xml.children()[4]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 9: // 清理宝石
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 10: // 消耗品
               this.xml_data = new DataProvider(this.xml.children()[5]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 11: // 清理消耗品
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 12: // 其他道具
               this.xml_data = new DataProvider(this.xml.children()[6]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 13: // 清理其他道具
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 14: // 任务道具
               this.xml_data = new DataProvider(this.xml.children()[9]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 15: // 清理任务道具
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 16: // 宠物
               this.xml_data = new DataProvider(this.xml.children()[8]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 17: // 宠物装备
               this.xml_data = new DataProvider(this.xml.children()[7]);
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 18: // 修改金币
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 19: // 修改等级
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 20: // 修改经验
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 21: // 修改血量
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 22: // 修改魔法值
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 23: // 无敌模式
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 24: // 秒杀模式
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 25: // 解锁所有技能
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 26: // 技能冷却清零
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 27: // 修改攻击力
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 28: // 修改防御力
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 29: // 修复背包
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 30: // 列出装备ID
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 31: // 输出所有装备ID
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 32: // 输出常用装备ID
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
            case 33: // 其他功能
               this.xml_data = new DataProvider();
               this.data_xml.dataProvider = this.xml_data;
               break;
         }
      }
      
      internal function __setProp_send_bt_场景1_图层1_0() : *
      {
         try
         {
            this.send_bt["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         this.send_bt.emphasized = false;
         this.send_bt.enabled = true;
         this.send_bt.label = "send";
         this.send_bt.labelPlacement = "right";
         this.send_bt.selected = false;
         this.send_bt.toggle = false;
         this.send_bt.visible = true;
         try
         {
            this.send_bt["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function __setProp_data_check_场景1_图层1_0() : *
      {
         var _loc2_:SimpleCollectionItem = null;
         var _loc3_:Array = null;
         var _loc4_:Object = null;
         var _loc5_:int = 0;
         var _loc6_:* = undefined;
         try
         {
            this.data_check["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         var _loc1_:DataProvider = new DataProvider();
         _loc3_ = [{
            "label":"添加武器装备",
            "data":1
         },{
            "label":"清理武器装备",
            "data":2
         },{
            "label":"添加衣服装备",
            "data":3
         },{
            "label":"清理衣服装备",
            "data":4
         },{
            "label":"添加项链装备",
            "data":5
         },{
            "label":"清理项链装备",
            "data":6
         },{
            "label":"添加葫芦装备",
            "data":7
         },{
            "label":"清理葫芦装备",
            "data":8
         },{
            "label":"添加宝石",
            "data":9
         },{
            "label":"清理宝石",
            "data":10
         },{
            "label":"添加消耗品",
            "data":11
         },{
            "label":"清理消耗品",
            "data":12
         },{
            "label":"添加其他道具",
            "data":13
         },{
            "label":"清理其他道具",
            "data":14
         },{
            "label":"添加任务道具",
            "data":15
         },{
            "label":"清理任务道具",
            "data":16
         },{
            "label":"添加宠物",
            "data":17
         },{
            "label":"添加宠物装备",
            "data":18
         },{
            "label":"修改金币",
            "data":19
         },{
            "label":"修改等级",
            "data":20
         },{
            "label":"修改经验",
            "data":21
         },{
            "label":"修改血量",
            "data":22
         },{
            "label":"修改魔法值",
            "data":23
         },{
            "label":"无敌模式",
            "data":24
         },{
            "label":"秒杀模式",
            "data":25
         },{
            "label":"解锁所有技能",
            "data":26
         },{
            "label":"技能冷却清零",
            "data":27
         },{
            "label":"修改攻击力",
            "data":28
         },{
            "label":"修改防御力",
            "data":29
         },{
            "label":"修复背包",
            "data":30
         },{
            "label":"列出装备ID",
            "data":31
         },{
            "label":"输出所有装备ID",
            "data":32
         },{
            "label":"输出常用装备ID",
            "data":33
         }];
         _loc5_ = 0;
         while(_loc5_ < _loc3_.length)
         {
            _loc2_ = new SimpleCollectionItem();
            _loc4_ = _loc3_[_loc5_];
            for(_loc6_ in _loc4_)
            {
               _loc2_[_loc6_] = _loc4_[_loc6_];
            }
            _loc1_.addItem(_loc2_);
            _loc5_++;
         }
         this.data_check.dataProvider = _loc1_;
         this.data_check.editable = false;
         this.data_check.enabled = true;
         this.data_check.prompt = "";
         this.data_check.restrict = "";
         this.data_check.rowCount = 5;
         this.data_check.visible = true;
         try
         {
            this.data_check["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function __setProp___id0__场景1_图层1_0() : *
      {
         try
         {
            this.__id0_["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         this.__id0_.autoSize = "none";
         this.__id0_.condenseWhite = false;
         this.__id0_.enabled = true;
         this.__id0_.htmlText = "";
         this.__id0_.selectable = false;
         this.__id0_.text = "填写物品id";
         this.__id0_.visible = true;
         this.__id0_.wordWrap = false;
         try
         {
            this.__id0_["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function __setProp___id1__场景1_图层1_0() : *
      {
         try
         {
            this.__id1_["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         this.__id1_.autoSize = "none";
         this.__id1_.condenseWhite = false;
         this.__id1_.enabled = true;
         this.__id1_.htmlText = "";
         this.__id1_.selectable = false;
         this.__id1_.text = "填写物品数量";
         this.__id1_.visible = true;
         this.__id1_.wordWrap = false;
         try
         {
            this.__id1_["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function __setProp_data_xml_场景1_图层1_0() : *
      {
         var _loc2_:SimpleCollectionItem = null;
         var _loc3_:Array = null;
         var _loc4_:Object = null;
         var _loc5_:int = 0;
         var _loc6_:* = undefined;
         try
         {
            this.data_xml["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         var _loc1_:DataProvider = new DataProvider();
         _loc3_ = [];
         _loc5_ = 0;
         while(_loc5_ < _loc3_.length)
         {
            _loc2_ = new SimpleCollectionItem();
            _loc4_ = _loc3_[_loc5_];
            for(_loc6_ in _loc4_)
            {
               _loc2_[_loc6_] = _loc4_[_loc6_];
            }
            _loc1_.addItem(_loc2_);
            _loc5_++;
         }
         this.data_xml.dataProvider = _loc1_;
         this.data_xml.editable = false;
         this.data_xml.enabled = true;
         this.data_xml.prompt = "";
         this.data_xml.restrict = "";
         this.data_xml.rowCount = 5;
         this.data_xml.visible = true;
         try
         {
            this.data_xml["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function __setProp_P_场景1_图层1_0() : *
      {
         var _loc2_:SimpleCollectionItem = null;
         var _loc3_:Array = null;
         var _loc4_:Object = null;
         var _loc5_:int = 0;
         var _loc6_:* = undefined;
         try
         {
            this.P["componentInspectorSetting"] = true;
         }
         catch(e:Error)
         {
         }
         var _loc1_:DataProvider = new DataProvider();
         _loc3_ = [{
            "label":"P1",
            "data":""
         },{
            "label":"P2",
            "data":""
         }];
         _loc5_ = 0;
         while(_loc5_ < _loc3_.length)
         {
            _loc2_ = new SimpleCollectionItem();
            _loc4_ = _loc3_[_loc5_];
            for(_loc6_ in _loc4_)
            {
               _loc2_[_loc6_] = _loc4_[_loc6_];
            }
            _loc1_.addItem(_loc2_);
            _loc5_++;
         }
         this.P.dataProvider = _loc1_;
         this.P.editable = false;
         this.P.enabled = true;
         this.P.prompt = "";
         this.P.restrict = "";
         this.P.rowCount = 5;
         this.P.visible = true;
         try
         {
            this.P["componentInspectorSetting"] = false;
         }
         catch(e:Error)
         {
         }
      }
      
      internal function frame1() : *
      {
         this.local_con = new LocalConnection();
         this.local_con.addEventListener(StatusEvent.STATUS,this.onStatus);
         this.send_bt.addEventListener(MouseEvent.CLICK,this.send);
         this.Load_Config();
         this.obj = new Object();

         // 添加装备数据接收器
         setupEquipmentDataReceiver();
      }

      // 新增：设置装备数据接收器
      private function setupEquipmentDataReceiver():void
      {
         try {
            var receiver:LocalConnection = new LocalConnection();
            receiver.client = this;
            receiver.connect("equipmentExporter");

            this.output.appendText("装备数据接收器已启动\n");
            this.output.appendText("等待装备数据传输...\n");

         } catch (e:Error) {
            this.output.appendText("启动装备数据接收器失败: " + e.message + "\n");
         }
      }

      // 新增：接收装备数据
      public function receiveEquipmentData(chunkIndex:int, totalChunks:int, data:String):void
      {
         try {
            this.output.appendText("接收数据块 " + (chunkIndex + 1) + "/" + totalChunks + "\n");

            // 这里可以处理接收到的数据块
            // 由于localcon_T主要是发送器，我们只做简单的日志记录

         } catch (e:Error) {
            this.output.appendText("接收数据失败: " + e.message + "\n");
         }
      }

      // 新增：导出完成回调
      public function exportComplete(totalChunks:int):void
      {
         try {
            this.output.appendText("装备数据导出完成！总共 " + totalChunks + " 个数据块\n");
            this.output.appendText("请查看桌面的装备ID列表文件\n");

         } catch (e:Error) {
            this.output.appendText("导出完成处理失败: " + e.message + "\n");
         }
      }
   }
}

